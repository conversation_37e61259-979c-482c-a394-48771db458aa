import { redirect } from 'next/navigation';
import { validateIframeToken, extractUserData<PERSON>romToken } from '@/lib/auth/jwt';
import { createOrGetExternalUser } from '@/lib/db/queries';
import { EmbedChatWrapper } from '@/components/embed-chat-wrapper';
import { generateUUID } from '@/lib/utils';

interface EmbedPageProps {
  searchParams: {
    token?: string;
    user_id?: string;
    source?: string;
    tenant?: string;
    user_name?: string;
  };
}

async function createEmbedSession(token?: string, userId?: string, source?: string, tenant?: string, userName?: string) {
  console.log('🔐 Creating embed session with:', { 
    hasToken: !!token, 
    userId, 
    source,
    tenant,
    userName,
    tokenLength: token?.length 
  });

  try {
    // If token is provided, try JWT-based authentication first
    if (token) {
      console.log('🎫 Attempting JWT authentication...');
      const userData = extractUserDataFromToken(token);
      
      // If JWT extraction succeeded, use it
      if (userData) {
        console.log('✅ JWT Token validated, user data:', userData);
        const user = await createOrGetExternalUser({
          ...userData,
          metadata: {
            ...userData.metadata,
            tenant,
            userName,
            lastAccess: new Date().toISOString(),
          },
        });
        console.log('✅ User created/retrieved:', { id: user.id, email: user.email });
        
        return {
          user: {
            id: user.id,
            email: user.email,
            type: 'external' as const,
            externalId: userData.externalId,
            source: userData.source,
            token,
            tenant,
            userName,
          },
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
        };
      }
      
      // If JWT extraction failed but we have userId, try UUID token validation
      if (userId) {
        console.log('🏷️ JWT extraction failed, trying UUID token with simple auth...');
        // TODO: Add UUID token validation against database or external service here
        // For now, we'll proceed with simple authentication
        const effectiveSource = source || 'iframe';
        const userData = {
          externalId: userId,
          source: effectiveSource as any,
          metadata: {
            tenant,
            userName,
            token, // Store the UUID token for reference
            lastAccess: new Date().toISOString(),
          },
        };

        const user = await createOrGetExternalUser(userData);
        console.log('✅ UUID token auth successful, user:', { id: user.id, email: user.email });
        
        return {
          user: {
            id: user.id,
            email: user.email,
            type: 'external' as const,
            externalId: userId,
            source: effectiveSource,
            token,
            tenant,
            userName,
          },
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        };
      }
      
      console.error('❌ Token provided but no valid authentication method available');
      throw new Error('Invalid token');
    }

    // Fallback: Simple user_id authentication (less secure)
    if (userId) {
      console.log('🔑 Attempting simple authentication with user_id...');
      const effectiveSource = source || 'iframe';
      const userData = {
        externalId: userId,
        source: effectiveSource as any,
        metadata: {
          tenant,
          userName,
          lastAccess: new Date().toISOString(),
        },
      };

      const user = await createOrGetExternalUser(userData);
      console.log('✅ Simple auth successful, user:', { id: user.id, email: user.email });
      
      return {
        user: {
          id: user.id,
          email: user.email,
          type: 'external' as const,
          externalId: userId,
          source: effectiveSource,
          token,
          tenant,
          userName,
        },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
    }

    console.error('❌ No valid authentication provided');
    throw new Error('No valid authentication provided');
  } catch (error) {
    console.error('💥 Failed to create embed session:', error);
    throw error;
  }
}

export default async function EmbedPage({ searchParams }: EmbedPageProps) {
  console.log('🚀 EmbedPage loading with searchParams:', searchParams);
  
  // Await searchParams to comply with Next.js 15 async API
  const params = await searchParams;
  const { token, user_id, source, tenant, user_name } = params;

  // Redirect to auth error if no authentication provided
  if (!token && !user_id) {
    console.log('❌ No authentication provided, redirecting to auth error');
    redirect('/embed/auth-error?error=missing_auth');
  }

  try {
    const session = await createEmbedSession(token, user_id, source, tenant, user_name);
    const id = generateUUID();

    console.log('🎉 Embed page loaded successfully with external session:', {
      externalId: session.user.externalId,
      source: session.user.source,
      tenant: session.user.tenant,
      userName: session.user.userName,
      chatId: id
    });

    return (
      <EmbedChatWrapper
        id={id}
        initialMessages={[]}
        initialChatModel="chat-model"
        initialVisibilityType="private"
        isReadonly={false}
        session={session}
        autoResume={false}
      />
    );
  } catch (error) {
    console.error('💥 Embed authentication failed:', error);
    const errorType = error instanceof Error && error.message.includes('Invalid token') 
      ? 'invalid_token' 
      : 'auth_failed';
    redirect(`/embed/auth-error?error=${errorType}`);
  }
}
